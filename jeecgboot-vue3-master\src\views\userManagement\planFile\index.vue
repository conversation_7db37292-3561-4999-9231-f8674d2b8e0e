<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined" v-auth="'planFile:add'">
          新增</a-button>
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls" v-auth="'planFile:export'">
          导出</a-button>
        <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls"
          v-auth="'planFile:import'">导入</j-upload-button>
        <a-button type="primary" preIcon="ant-design:file-search-outlined" @click="actionRecord"
          v-auth="'planFile:log'"> 操作日志</a-button>
        <a-button type="primary" preIcon="ant-design:file-search-outlined" @click="controlledDocuments"
          v-auth="'planFile:controlled'"> 受控文件清单</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>

    <!-- 表单区域 -->
    <PlanFile @register="registerModal" @success="handleSuccess"></PlanFile>
    <PlanAuthorizationModal @register="regAuthorizationModal" @success="handleSuccess"></PlanAuthorizationModal>
    <FileViewPrintModal @register="regFileModal" @success="handleSuccess"></FileViewPrintModal>
    <PlanLogModal @register="regLogModal"></PlanLogModal>
    <FileHistoryModal @register="regHistoryModal"></FileHistoryModal>
  </div>
</template>

<script lang="ts" name="PlanFile" setup>
import { ref, computed, unref } from 'vue';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { useListPage } from '/@/hooks/system/useListPage';
import PlanFile from './modules/PlanFileModal.vue';
import PlanAuthorizationModal from './modules/PlanAuthorizationModal.vue';
import FileViewPrintModal from './modules/FileViewPrintModal.vue';
import PlanLogModal from './modules/PlanLogModal.vue';
import FileHistoryModal from './modules/FileHistoryModal.vue';
import { columns, searchFormSchema } from './PlanFile.data';
import { useUserStore } from '/@/store/modules/user';
import { usePermission } from '/@/hooks/web/usePermission';
import { list, deleteOne, batchDelete, getImportUrl, getExportUrl, toAudit, toCancel } from './PlanFile.api';
import { hiprint } from 'vue-plugin-hiprint';
import panel from './printjs.js'; //模板
import { defHttp } from '/@/utils/http/axios';
const userStore = useUserStore();
const { hasPermission } = usePermission();
//注册model
const [registerModal, { openModal }] = useModal();
const [regAuthorizationModal, { openModal: openAuthorizationModal }] = useModal();
const [regFileModal, { openModal: openFileModal }] = useModal();
const [regLogModal, { openModal: openLogModal }] = useModal();
const [regHistoryModal, { openModal: openHistoryModal }] = useModal();

//注册table数据
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    title: '人员管理-文件清单',
    api: list,
    // beforeFetch: (params)=>{
    //   params.status='0'
    //   return params
    // },
    columns,
    canResize: false,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      fieldMapToTime: [['timeRange', ['planStartTime', 'planEndTime'], 'YYYY-MM-DD']],
    },
    actionColumn: {
      width: 120,
    },
  },
  exportConfig: {
    name: '人员管理-文件清单',
    url: getExportUrl,
  },
  importConfig: {
    url: getImportUrl,
  },
});

const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;
function controlledDocuments() {
  defHttp.get({ url: '/lims/employee/getFileInfoChooseList?ids=' + selectedRowKeys.value.join(',') }).then((res) => {
    let tableData = res.map((item, index) => {
      return {
        index: index + 1,
        ...item
      }
    })
    let arr = [{
      tableData
    }]
    console.log("🚀 ~ controlledDocuments ~ arr:", arr)
    new hiprint.PrintTemplate({ template: panel }).print(arr);
  })
}
/**
 * 新增事件
 */
function handleAdd() {
  openModal(true, {
    isAdd: true,
    showFooter: true,
  });
}
/**
 * 变更事件
 */
function handleUpdate(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  openModal(true, {
    record,
    isEdit: true,
    showFooter: true,
  });
}
/**
 * 编辑事件
 */
function handleCommit(record: Recordable) {
  openModal(true, {
    record,
    isCommit: true,
    showFooter: true,
  });
}
/**
 * 技术负责人审批
 */
function handleAudit1(record: Recordable) {
  openModal(true, {
    record,
    audit: 'audit1',
    showFooter: true,
  });
}
// 实验室管理员审批
function handleAudit2(record: Recordable) {
  openModal(true, {
    record,
    audit: 'audit2',
    showFooter: true,
  });
}
// 综合管理员审批
function handleAudit3(record: Recordable) {
  openModal(true, {
    record,
    audit: 'audit3',
    showFooter: true,
  });
}
/**
 * 详情
 */
function handleDetail(record: Recordable) {
  openFileModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });

}
/**
 * 操作记录
 */
function actionRecord(record: Recordable) {
  openLogModal(true, {
    // record: { id: record.id },
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteOne({ id: record.id }, reload);
}
/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  await batchDelete({ ids: selectedRowKeys.value }, reload);
}
/**
 * 审核
 */
async function handleAudit(record) {
  await toAudit({ id: record.id }, reload);
}
// 作废
async function handleCancel(record) {
  await toCancel({ id: record.id }, reload);
}
/**
 * 成功回调
 */
function handleSuccess({ isUpdate, values }) {
  reload();
}
/**
 * 历史记录
 */
function handleHistory(record) {
  openHistoryModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 授权
 */
function handleAuthorization(record) {
  openAuthorizationModal(true, {
    record: { id: record.id, authorizedPeopleName: record.authorizedPeopleName, authorizedPeople: record.authorizedPeople },
    isUpdate: true,
    showFooter: true,
  });
}

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '打印',
      onClick: handleDetail.bind(null, record),
      auth: 'planFile:print',
      ifShow: () => {
        let code = userStore.getUserInfo.username
        return record.status == '0' && (hasPermission('planFile:detail') || (record.authorizedPeople && record.authorizedPeople.indexOf(code) != -1));
      },
    },
  ];
}
/**
 * 下拉操作栏 
 * status 0:正常 2:作废  1:失效
 */
function getDropDownAction(record) {
  return [
    {
      label: '更新',
      onClick: handleUpdate.bind(null, record),
      ifShow: () => {
        return record.auditStatus == '3'  && record.status == '0';
      },
      auth: 'planFile:edit',
    },
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      ifShow: () => {
           return (record.auditStatus == '' || record.auditStatus == null || record.auditStatus == '99') && record.status == '0';
      },
      auth: 'planFile:edit',
    },
    {
      label: '提交',
      onClick: handleCommit.bind(null, record),
      ifShow: () => {
        return (record.auditStatus == '' || record.auditStatus == null || record.auditStatus == '99') && record.status == '0';
      },
      auth: 'planFile:edit',
    },
    {
      label: '技术负责人审批',
      onClick: handleAudit1.bind(null, record),
      ifShow: () => {
        return record.auditStatus == '0';
      },
      // auth: 'planFile:技术负责人审批',
    },
    {
      label: '实验室管理员审批',
      onClick: handleAudit2.bind(null, record),
      ifShow: () => {
        return record.auditStatus == '1' && record.status == '0';
      },
      // auth: 'planFile:实验室管理员审批',
    },
    {
      label: '综合管理员审批',
      onClick: handleAudit3.bind(null, record),
      ifShow: () => {
        return record.auditStatus == '2' && record.status == '0';
      },
      // auth: 'planFile:综合管理员审批',
    },
    {
      label: '历史记录',
      onClick: handleHistory.bind(null, record),
      ifShow: () => {
        let code = userStore.getUserInfo.username
        return hasPermission('planFile:history') || (record.authorizedPeople && record.authorizedPeople.indexOf(code) != -1);
      },
    },
    // {
    //   label: '审核',
    //   popConfirm: {
    //     title: '是否确认审核',
    //     confirm: handleAudit.bind(null, record),
    //   },
    //   auth: 'planFile:audit',
    //   ifShow: () => {
    //     return record.status == '0' && record.isAudit == '0';
    //   },
    // },
    {
      label: '作废',
      popConfirm: {
        title: '是否确认作废',
        confirm: handleCancel.bind(null, record),
      },
      auth: 'planFile:cancel',
      ifShow: () => {
        return record.auditStatus == '3' && record.status == '0';
      },
    },
    {
      label: '授权',
      onClick: handleAuthorization.bind(null, record),
      ifShow: () => {
        return record.auditStatus == '3' && record.status == '0';
      },
      auth: 'planFile:authorization',
    },

  ];
}
</script>
<style scoped></style>