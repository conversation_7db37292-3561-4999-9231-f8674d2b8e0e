<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="60%">
    <a-form ref="formRef" :model="orderMainModel" @submit="handleSubmit" :label-col="labelCol" :wrapper-col="wrapperCol"
      :rules="validatorRules">

      <a-row class="form-row" :gutter="8">
        <a-col :span="20">
          <a-form-item label="文件编号" name="fileCode">
            <a-input :disabled="disabled" v-model:value="orderMainModel.fileCode" placeholder="文件编号" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="文件名称" name="fileName">
            <a-input :disabled="disabled" v-model:value="orderMainModel.fileName" placeholder="文件名称" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="文件版本号" name="fileVersion">
            <a-input :disabled="disabled" v-model:value="orderMainModel.fileVersion" placeholder="文件版本号" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="生效日期" name="effectiveDate">
            <a-date-picker :disabled="disabled" v-model:value="orderMainModel.effectiveDate" :valueFormat="'YYYY-MM-DD'" placeholder="生效日期" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="申请变更原因" name="changeReason">
            <a-input :disabled="disabled" v-model:value="orderMainModel.changeReason" placeholder="申请变更原因" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="变更前内容" name="beforeChangeContent">
            <a-input :disabled="disabled" v-model:value="orderMainModel.beforeChangeContent" placeholder="变更前内容" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="变更后内容" name="afterChangeContent">
            <a-input :disabled="disabled" v-model:value="orderMainModel.afterChangeContent" placeholder="变更后内容" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="变更方式" name="changeType">
            <a-select :disabled="disabled" placeholder="变更方式" v-model:value="orderMainModel.changeType">
              <a-select-option :value="'换页'">换页</a-select-option>
              <a-select-option :value="'换版'">换版</a-select-option>
              <a-select-option :value="'修订'">修订</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="20" v-show="false">
          <a-form-item label="SOP" name="sopId">
            <a-input :disabled="!isFooter" v-model:value="orderMainModel.sopId" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="SOP" name="sopName">
            <a-input disabled v-model:value="orderMainModel.sopName" />
          </a-form-item>
        </a-col>
        <a-col :span="2">
          <a-button @click="chooseSop">选择SOP</a-button>
        </a-col>
        <a-col :span="20">
          <a-form-item label="文件" name="fileUrl">
            <!-- <template #label>
              <span style="color:red;font-size:18px;color:#ff4949;">* </span> &nbsp; 文件
            </template> -->
            <JUpload :disabled="disabled" v-model:value="orderMainModel.fileUrl" bizPath="strainConfirm"
              :download="false" :uploadGoOn="false" :maxCount="1" :beforeUpload="extendBeforeUpload"></JUpload>
          </a-form-item>
        </a-col>
        <a-col :span="20" v-if="isUpdate">
          <a-form-item label="更新原因" name="updateReason">
            <a-textarea v-model:value="orderMainModel.updateReason" rows="2" show-count :maxlength="150" />
            <!-- <a-input v-model:value="orderMainModel.updateReason" /> -->
          </a-form-item>
        </a-col>

        <a-col :span="20" v-if="audit">
          <a-form-item label="技术负责人审核意见" v-if=orderMainModel.auditContent>
            <a-textarea disabled v-model:value="orderMainModel.auditContent" rows="2" show-count :maxlength="150" />
          </a-form-item>
          <a-form-item label="实验室管理员审核意见" v-if=orderMainModel.secondAuditContent>
            <a-textarea disabled v-model:value="orderMainModel.secondAuditContent" rows="2" show-count :maxlength="150" />
          </a-form-item>
          <a-form-item label="综合管理员审核意见" v-if=orderMainModel.lastAuditPerson>
            <a-textarea disabled v-model:value="orderMainModel.lastAuditPerson" rows="2" show-count :maxlength="150" />
          </a-form-item>
          <a-form-item label="审核意见">
            <a-textarea v-model:value="orderMainModel.content" rows="2" show-count :maxlength="150" />
            <!-- <a-input v-model:value="orderMainModel.updateReason" /> -->
          </a-form-item>
        </a-col>

      </a-row>
    </a-form>
    <template #centerFooter>
      <a-button v-if="audit" @click="handleReject" color="error" :loading="load">
        驳回
      </a-button>
    </template>
  </BasicModal>
  <SelectSopModal @register="regSopModal" @success="handleReturn"></SelectSopModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, unref, watchEffect } from 'vue';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form/index';
import { formSchema } from '../PlanFile.data';
import { saveOrUpdate } from '../PlanFile.api';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
import SelectSopModal from './SelectSopModal.vue';
import { defHttp } from '/@/utils/http/axios';
import dayjs from 'dayjs';

// Emits声明
const emit = defineEmits(['register', 'success']);
const formRef = ref<FormInstance>();
const isAdd = ref(true);
const isUpdate = ref(true);
const isEdit = ref(true);
const isCommit = ref(true);
const isFooter = ref(true);
const title = ref('')

const audit = ref(null);
const load = ref(false);

const disabled = ref(false);
const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 7 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 16 },
});
const orderMainModel = reactive({
  id: null,
  fileCode: null,
  fileName: null,
  sopId: null,
  sopName: null,
  fileUrl: null,
  status: null,
  updateReason: null,
  fileVersion: null,
  effectiveDate: dayjs(),
  changeReason: null,
  beforeChangeContent: null,
  afterChangeContent: null,
  changeType: null,

  content: null,

  auditContent: null,
  auditStatus: null,
  secondAuditContent: null,
  lastAuditPerson: null,
});

const validatorRules = {
  fileCode: [
    { required: true, message: '必填！' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }
  ],
  fileName: [
    { required: true, message: '必填！' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }
  ],
  updateReason: [
    { required: true, message: '必填！' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }
  ],
  // sopName: [
  //   { required: true, message: '必填！' }
  // ],
  fileUrl: [{ required: true, message: '必传！' }],
  fileVersion: [{ required: true, message: '必填！' }],
  effectiveDate: [{ required: true, message: '必填！' }],
  changeReason: [{ required: true, message: '必填！' }],
  beforeChangeContent: [{ required: true, message: '必填！' }],
  afterChangeContent: [{ required: true, message: '必填！' }],
  changeType: [{ required: true, message: '必填！' }]
};
const [regSopModal, { openModal: openSopModal }] = useModal();
//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  //重置表单
  reset();
  
  setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  isEdit.value = !!data?.isEdit;
  isAdd.value = !!data?.isAdd;
  isCommit.value = !!data?.isCommit;
  audit.value = data?.audit;
  isFooter.value = !!data?.showFooter;
  if (unref(isUpdate)) {
    title.value = '更新'
    //表单赋值
    disabled.value = false;
    Object.assign(orderMainModel, data.record);
  } else if (unref(isEdit)) {
    title.value = '编辑'
    //表单赋值
    disabled.value = false;
    Object.assign(orderMainModel, data.record);
  } else if (unref(isCommit)) {
    title.value = '提交'
    //表单赋值
    disabled.value = true;
    Object.assign(orderMainModel, data.record);
  } else if (audit.value) {
    title.value = '审批'
    //表单赋值
    disabled.value = true;
    Object.assign(orderMainModel, data.record);
  }else{
    title.value = '新增'
  }
  orderMainModel.effectiveDate=dayjs(orderMainModel.effectiveDate)
  console.log('🚀 ~ file:  ~ orderMainModel:', orderMainModel);
});
//设置标题
//表单提交事件
function handleSubmit(v) {
  formRef.value
    .validate()
    .then(async () => {
      try {
        setModalProps({ confirmLoading: true });

        console.log("🚀 ~ .then ~ orderMainModel.fileList:", orderMainModel.fileList)
        if (orderMainModel.fileUrl && orderMainModel.fileUrl[0] && orderMainModel.fileUrl[0].response) {
          orderMainModel.fileUrl = orderMainModel.fileUrl[0].response.message;
        }
        // for(let j = 0; j < orderMainModel.fileList.length; j++) {

        //   orderMainModel.fileUrl += orderMainModel.fileList[j].response.message;
        //   if(j + 1 != orderMainModel.fileList.length) {
        //     orderMainModel.fileUrl += ',';
        //   }
        // }
        //提交表单
        if (isEdit.value) {
          await defHttp.put({ url: '/lims/employee/fileInfoEdit', params: orderMainModel })
        } if (isUpdate.value) {
          await defHttp.put({ url: '/lims/employee/fileInfoRollBackEdit', params: orderMainModel })
        }  else if (isCommit.value) {
          await defHttp.post({ url: '/lims/employee/fileInfoCommit', params: { id: orderMainModel.id } })
        } else if (isAdd.value) {
          await saveOrUpdate(orderMainModel, isUpdate.value);
        } else if (audit.value) {
          let params;
          if(audit.value=='audit1'){
            params = {
              id: orderMainModel.id,
              auditStatus: Number(orderMainModel.auditStatus)+1,
              auditContent: orderMainModel.content
            }
          }else if(audit.value=='audit2'){
            params = {
              id: orderMainModel.id,
              auditStatus: Number(orderMainModel.auditStatus)+1,
              secondAuditContent: orderMainModel.content
            }
          }else if(audit.value=='audit3'){
            params = {
              id: orderMainModel.id,
              auditStatus: Number(orderMainModel.auditStatus)+1,
              lastAuditContent: orderMainModel.content
            }
          }
          await defHttp.post({ url: '/lims/employee/fileInfoAuditOrRollBack', params })
        }
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, orderMainModel });
      } finally {
        setModalProps({ confirmLoading: false });
      }
    })
    .catch((error: ValidateErrorEntity<any>) => {
      console.log('error', error);
    });
}
async function handleReject() {
  if(!orderMainModel.content){
    message.error('请填写驳回理由！')
    return
  }
  try {
    setModalProps({ confirmLoading: true });
    load.value = true;
    let params = {
      id: orderMainModel.id,
      auditStatus: 99,
      rejectReason: orderMainModel.content
    }
    await defHttp.post({ url: '/lims/employee/fileInfoAuditOrRollBack', params })
    closeModal();
    //刷新列表
    emit('success', { isUpdate: isUpdate.value, orderMainModel });
  } finally {
    setModalProps({ confirmLoading: false });
    load.value = false
  }
}
function reset() {
  orderMainModel.id = null;
  orderMainModel.fileCode = null;
  orderMainModel.fileName = null;
  orderMainModel.sopId = null;
  orderMainModel.sopName = null;
  orderMainModel.fileUrl = null;
  orderMainModel.status = null;
  orderMainModel.updateReason = null;
  orderMainModel.fileVersion = null;
  orderMainModel.effectiveDate = dayjs();
  orderMainModel.changeReason = null;
  orderMainModel.beforeChangeContent = null;
  orderMainModel.afterChangeContent = null;
  orderMainModel.changeType = null;

  orderMainModel.content = null;
  orderMainModel.auditContent = null;
  orderMainModel.auditStatus = null;
  orderMainModel.secondAuditContent = null;
  orderMainModel.lastAuditPerson = null;
}

function extendBeforeUpload(pro) {
  console.log('beforeload', pro)
  console.log('beforeloadType', pro.type)
  //  && pro.type != 'application/vnd.ms-excel' && pro.type != 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' && pro.type != 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  if (pro.type != 'application/pdf' || pro.size > 20971520) {
    console.log('orderMainModel.fileUrl', orderMainModel.fileUrl)
    message.warning('请上传 <20MB 的 .pdf 文件');
    // message.warning('请上传 .pdf .xls .xlsx .docx 文件');
    return false;
  }

}

function chooseSop() {
  openSopModal(true, {

    isUpdate: true,
    showFooter: true,
  });
}
function handleReturn(data) {
  console.log('handleReturn', data)
  orderMainModel.sopId = data[0].id;
  orderMainModel.sopName = data[0].name;
}

watchEffect(() => {
  console.log('inventoryModel.fileUrl', orderMainModel.fileUrl)
  if (orderMainModel.fileUrl) {
    formRef.value.validateFields()
  }
});
</script>

<style lang="less" scoped>
.fontColor {
  color: black;
}
</style>