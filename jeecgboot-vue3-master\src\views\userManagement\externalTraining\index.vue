<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <!-- <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button> -->
        <!-- <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
        <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
<a-button>批量操作
  <Icon icon="mdi:chevron-down"></Icon>
</a-button>
</a-dropdown> -->
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>

    <!-- 表单区域 -->
    <externalTrainingModal @register="registerModal" @success="handleSuccess"></externalTrainingModal>
    <!-- 审批表打印 -->
    <SPBModal @register="registerSPBModal" @success="handleSuccess"></SPBModal>

    <!-- 驳回理由弹窗 -->
    <a-modal v-model:visible="rejectModalVisible" :title="rejectModalTitle" @ok="handleRejectConfirm"
      @cancel="handleRejectCancel" width="500px" :confirm-loading="rejectLoading">
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="培训班名称" name="className">
          <a-input v-model:value="orderMainModel.className" placeholder="请输入培训班名称" disabled />
        </a-form-item>

        <a-form-item label="外派培训原因" name="trainReason">
          <a-input v-model:value="orderMainModel.trainReason" placeholder="请输入外派培训原因" disabled />
        </a-form-item>

        <a-form-item label="培训目的" name="trainPurpose">
          <a-input v-model:value="orderMainModel.trainPurpose" placeholder="请输入培训目的" disabled />
        </a-form-item>

        <a-form-item label="培训地点" name="trainPlace">
          <a-input v-model:value="orderMainModel.trainPlace" placeholder="请输入培训地点" disabled />
        </a-form-item>

        <a-form-item label="举办单位" name="organizingUnit">
          <a-input v-model:value="orderMainModel.organizingUnit" placeholder="请输入举办单位" disabled />
        </a-form-item>

        <a-form-item label="实际课时总数" name="trainNumber">
          <a-input v-model:value="orderMainModel.trainNumber" placeholder="请输入实际课时总数" disabled />
        </a-form-item>

        <a-form-item label="预算费用" name="estimatedCost">
          <a-input v-model:value="orderMainModel.estimatedCost" placeholder="请输入预算费用" disabled />
        </a-form-item>

        <a-form-item label="实际费用" name="realCost">
          <a-input v-model:value="orderMainModel.realCost" placeholder="请输入实际费用" disabled />
        </a-form-item>

        <a-form-item label="有无相关证书" name="relatedCertificates">
          <a-radio-group v-model:value="orderMainModel.relatedCertificates" disabled>
            <a-radio value="有">有</a-radio>
            <a-radio value="无">无</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="部门所属体系" name="departmentSystem">
          <a-select v-model:value="orderMainModel.departmentSystem" placeholder="请选择部门所属体系" disabled>
            <a-select-option value="供应链体系">供应链体系</a-select-option>
            <a-select-option value="产品开发中心">产品开发中心</a-select-option>
            <a-select-option value="ODM业务部">ODM业务部</a-select-option>
            <a-select-option value="日化事业部">日化事业部</a-select-option>
            <a-select-option value="电商生态事业部">电商生态事业部</a-select-option>
            <a-select-option value="其他部门">其他部门</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="开始时间" name="startTime">
          <a-input v-model:value="orderMainModel.startTime" disabled />
        </a-form-item>
        <a-form-item label="结束时间" name="endTime">
          <a-input v-model:value="orderMainModel.endTime" disabled />
        </a-form-item>

        <a-form-item label="预算费用明细" name="estimatedCostUrl">
          <j-upload v-model:value="orderMainModel.estimatedCostUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
        </a-form-item>

        <a-form-item label="费用明细" name="costDetailUrl">
          <j-upload v-model:value="orderMainModel.costDetailUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
        </a-form-item>

        <a-form-item label="培训课程内容记录" name="recordUrl">
          <j-upload v-model:value="orderMainModel.recordUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
        </a-form-item>

        <a-form-item label="培训心得体会" name="experienceUrl">
          <j-upload v-model:value="orderMainModel.experienceUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
        </a-form-item>

        <a-form-item label="授课老师评估/ 培训服务机构评估" name="assessmentUrl">
          <j-upload v-model:value="orderMainModel.assessmentUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
        </a-form-item>

        <a-form-item label="人员工号" name="userCode">
          <a-input disabled v-model:value="orderMainModel.userCode" placeholder="请输入人员工号" />
        </a-form-item>
        <a-form-item label="人员名称" name="userName">
          <a-input :class="{ fontColor: true }" disabled v-model:value="orderMainModel.userName"
            placeholder="请输入人员名称" />
        </a-form-item>
        <a-form-item label="技术负责人意见" name="auditContent" v-if='orderMainModel.auditContent'>
          <a-textarea :rows="4" disabled v-model:value="orderMainModel.auditContent" />
        </a-form-item>
        <a-form-item label="实验室经理意见" name="assignPerson" v-if='orderMainModel.assignPerson'>
          <a-textarea :rows="4" disabled v-model:value="orderMainModel.assignPerson" />
        </a-form-item>
        <a-form-item label="驳回原因" name="rejectReason" v-if='orderMainModel.rejectReason'>
          <a-textarea :rows="4" disabled v-model:value="orderMainModel.rejectReason" />
        </a-form-item>



        <a-form-item label="驳回理由" :rules="[{ required: true, message: '请输入驳回理由' }]">
          <a-textarea v-model:value="rejectReason" placeholder="请输入驳回理由" :rows="4" :maxlength="200" show-count />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 上传证书弹窗 -->
    <a-modal v-model:visible="uploadModalVisible" title="上传证书" @ok="handleUploadConfirm" @cancel="handleUploadCancel"
      width="1000px" :confirm-loading="uploadLoading">
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="证书文件" :rules="[{ required: true, message: '请上传证书文件' }]">
          <j-upload v-model:value="certificateUrl" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 意见填写弹窗 -->
    <a-modal v-model:visible="commentModalVisible" :title="commentModalTitle" @ok="handleCommentConfirm"
      @cancel="handleCommentCancel" width="1000px" :confirm-loading="commentLoading">
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="培训班名称" name="className">
          <a-input v-model:value="currentCommentRecord.className" placeholder="请输入培训班名称" disabled />
        </a-form-item>

        <a-form-item label="外派培训原因" name="trainReason">
          <a-input v-model:value="currentCommentRecord.trainReason" placeholder="请输入外派培训原因" disabled />
        </a-form-item>

        <a-form-item label="培训目的" name="trainPurpose">
          <a-input v-model:value="currentCommentRecord.trainPurpose" placeholder="请输入培训目的" disabled />
        </a-form-item>

        <a-form-item label="培训地点" name="trainPlace">
          <a-input v-model:value="currentCommentRecord.trainPlace" placeholder="请输入培训地点" disabled />
        </a-form-item>

        <a-form-item label="举办单位" name="organizingUnit">
          <a-input v-model:value="currentCommentRecord.organizingUnit" placeholder="请输入举办单位" disabled />
        </a-form-item>

        <a-form-item label="实际课时总数" name="trainNumber">
          <a-input v-model:value="currentCommentRecord.trainNumber" placeholder="请输入实际课时总数" disabled />
        </a-form-item>

        <a-form-item label="预算费用" name="estimatedCost">
          <a-input v-model:value="currentCommentRecord.estimatedCost" placeholder="请输入预算费用" disabled />
        </a-form-item>

        <a-form-item label="实际费用" name="realCost">
          <a-input v-model:value="currentCommentRecord.realCost" placeholder="请输入实际费用" disabled />
        </a-form-item>

        <a-form-item label="有无相关证书" name="relatedCertificates">
          <a-radio-group v-model:value="currentCommentRecord.relatedCertificates" disabled>
            <a-radio value="有">有</a-radio>
            <a-radio value="无">无</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="部门所属体系" name="departmentSystem">
          <a-select v-model:value="currentCommentRecord.departmentSystem" placeholder="请选择部门所属体系" disabled>
            <a-select-option value="供应链体系">供应链体系</a-select-option>
            <a-select-option value="产品开发中心">产品开发中心</a-select-option>
            <a-select-option value="ODM业务部">ODM业务部</a-select-option>
            <a-select-option value="日化事业部">日化事业部</a-select-option>
            <a-select-option value="电商生态事业部">电商生态事业部</a-select-option>
            <a-select-option value="其他部门">其他部门</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="开始时间" name="duration">
          <a-input v-model:value="currentCommentRecord.startTime" disabled />
        </a-form-item>
        <a-form-item label="结束时间" name="duration">
          <a-input v-model:value="currentCommentRecord.endTime" disabled />
        </a-form-item>

        <a-form-item label="预算费用明细" name="estimatedCostUrl">
          <j-upload v-model:value="currentCommentRecord.estimatedCostUrl" :fileMaxSize="50" :fileMaxNumber="10"
            disabled />
        </a-form-item>

        <a-form-item label="费用明细" name="costDetailUrl">
          <j-upload v-model:value="currentCommentRecord.costDetailUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
        </a-form-item>

        <a-form-item label="培训课程内容记录" name="recordUrl">
          <j-upload v-model:value="currentCommentRecord.recordUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
        </a-form-item>

        <a-form-item label="培训心得体会" name="experienceUrl">
          <j-upload v-model:value="currentCommentRecord.experienceUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
        </a-form-item>

        <a-form-item label="授课老师评估/ 培训服务机构评估" name="assessmentUrl">
          <j-upload v-model:value="currentCommentRecord.assessmentUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
        </a-form-item>

        <a-form-item label="人员工号" name="userCode">
          <a-input disabled v-model:value="currentCommentRecord.userCode" placeholder="请输入人员工号" />
        </a-form-item>
        <a-form-item label="人员名称" name="userName">
          <a-input :class="{ fontColor: true }" disabled v-model:value="currentCommentRecord.userName"
            placeholder="请输入人员名称" />
        </a-form-item>
        <a-form-item label="技术负责人意见" name="auditContent" v-if='currentCommentRecord.auditContent'>
          <a-textarea :rows="4" disabled v-model:value="currentCommentRecord.auditContent" />
        </a-form-item>
        <a-form-item label="实验室经理意见" name="assignPerson" v-if='currentCommentRecord.assignPerson'>
          <a-textarea :rows="4" disabled v-model:value="currentCommentRecord.assignPerson" />
        </a-form-item>
        <a-form-item label="驳回原因" name="rejectReason" v-if='currentCommentRecord.rejectReason'>
          <a-textarea :rows="4" disabled v-model:value="currentCommentRecord.rejectReason" />
        </a-form-item>
        <a-form-item label="意见内容" :rules="[{ required: true, message: '请输入意见内容' }]">
          <a-textarea v-model:value="commentContent" placeholder="请输入意见内容（可选）" :rows="4" :maxlength="200" show-count />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" name="userManagement-externalTraining-index" setup>
import { ref, computed, unref } from 'vue';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { useListPage } from '/@/hooks/system/useListPage';
import externalTrainingModal from './modules/externalTrainingModal.vue';
import SPBModal from './modules/SPBModal.vue';
import { columns, searchFormSchema } from './externalTraining.data';
import { list, deleteOne, batchDelete, getImportUrl, getExportUrl, returnOne } from './externalTraining.api';
import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';

//注册model
const [registerModal, { openModal }] = useModal();
const [registerSPBModal, { openModal: openSPBModal }] = useModal();

// 驳回弹窗相关状态
const orderMainModel = ref<any>({});
const rejectModalVisible = ref(false);
const rejectModalTitle = ref('');
const rejectReason = ref('');
const currentRejectRecord = ref(null);
const rejectType = ref(''); // 'upAudit' 或 'audit'
const rejectLoading = ref(false); // 驳回操作loading状态

// 上传证书弹窗相关状态
const uploadModalVisible = ref(false);
const certificateUrl = ref('');
const currentUploadRecord = ref(null);
const uploadLoading = ref(false); // 上传操作loading状态

// 意见填写弹窗相关状态
const commentModalVisible = ref(false);
const commentModalTitle = ref('');
const commentContent = ref('');
const currentCommentRecord = ref<any>(null);
const commentType = ref(''); // 'audit' 或 'upAudit'
const commentLoading = ref(false); // 意见确认操作loading状态
//注册table数据
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    title: '人员管理-外部培训',
    api: list,
    columns,
    canResize: false,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      fieldMapToTime: [['timeRange', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    },
    showActionColumn: true,
    showTableSetting: false,
    actionColumn: {
      width: 120,
    },
  },
  exportConfig: {
    name: '人员管理-外部培训',
    url: getExportUrl,
  },
  importConfig: {
    url: getImportUrl,
  },
});

const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

/**
 * 新增事件
 */
function handleAdd() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}
/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteOne({ id: record.id }, reload);
}
/**
 * 退回事件
 */
async function handleReturn(record) {
  await returnOne({ id: record.id }, reload);
}
/**
 * 人员外出培训审批表
 */
function handleSeeSPB(record) {
  openSPBModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  await batchDelete({ ids: selectedRowKeys.value }, reload);
}
/**
 *  技术审核
 */
function handleAudit(record) {
  orderMainModel.value = record;
  console.log("🚀 ~ handleAudit ~ orderMainModel.value:", orderMainModel.value)
  currentCommentRecord.value = record;
  commentType.value = 'audit';
  commentModalTitle.value = '技术审核意见';
  commentContent.value = '';
  commentModalVisible.value = true;
}
/**
 * 同意
 */
function handleUpAudit(record) {
  currentUploadRecord.value = record;
  currentCommentRecord.value = record;
  commentType.value = 'upAudit';
  commentModalTitle.value = '同意意见';
  commentContent.value = '';
  commentModalVisible.value = true;
}
/**
 * 发起审批驳回
 */
function handleUpAuditReject(record) {
  orderMainModel.value = { ...record };
  currentRejectRecord.value = record;
  rejectType.value = 'upAudit';
  rejectModalTitle.value = '发起审批驳回';
  rejectReason.value = '';
  rejectModalVisible.value = true;
}

/**
 * 技术审核驳回
 */
function handleAuditReject(record) {
  orderMainModel.value = { ...record };
  currentRejectRecord.value = record;
  rejectType.value = 'audit';
  rejectModalTitle.value = '技术审核驳回';
  rejectReason.value = '';
  rejectModalVisible.value = true;
}

/**
 * 驳回确认
 */
async function handleRejectConfirm() {
  if (!rejectReason.value.trim()) {
    message.error('请输入驳回理由');
    return;
  }

  if (rejectLoading.value) {
    return; // 防止重复提交
  }

  rejectLoading.value = true;
  try {
    const params: any = {
      id: currentRejectRecord.value.id,
    };
    // 根据驳回类型设置不同的理由字段
    if (rejectType.value === 'upAudit') {
      params.auditContent = rejectReason.value;
      params.auditStatus = 99;
    } else if (rejectType.value === 'audit') {
      params.rejectReason = rejectReason.value;
      params.auditStatus = 99;
    }
    await defHttp.post({
      url: '/lims/employee/outTrainingAuditOrRollBack',
      params,
    });
    rejectModalVisible.value = false;
    reload();
  } catch (error) {
    // 可以在这里添加错误处理
  } finally {
    rejectLoading.value = false;
  }
}

/**
 * 驳回取消
 */
function handleRejectCancel() {
  rejectModalVisible.value = false;
  rejectReason.value = '';
  currentRejectRecord.value = null;
}

/**
 * 上传证书
 */
function handleUpload(record) {
  currentUploadRecord.value = record;
  certificateUrl.value = record.certificateUrl;
  uploadModalVisible.value = true;
}

/**
 * 上传证书确认
 */
async function handleUploadConfirm() {
  if (!certificateUrl.value) {
    message.error('请上传证书文件');
    return;
  }

  if (uploadLoading.value) {
    return; // 防止重复提交
  }

  uploadLoading.value = true;
  try {
    await defHttp.put({
      url: '/lims/employee/uploadOutTrainingData',
      data: {
        id: currentUploadRecord.value.id,
        certificateUrl: certificateUrl.value,
      },
    });

    message.success('证书上传成功');
    uploadModalVisible.value = false;
    reload();
  } catch (error) {
    // message.error('证书上传失败');
  } finally {
    uploadLoading.value = false;
  }
}

/**
 * 上传证书取消
 */
function handleUploadCancel() {
  uploadModalVisible.value = false;
  certificateUrl.value = '';
  currentUploadRecord.value = null;
}

/**
 * 意见确认
 */
async function handleCommentConfirm() {
  if (!commentContent.value.trim()) {
    message.error('请输入意见');
    return;
  }

  if (commentLoading.value) {
    return; // 防止重复提交
  }

  commentLoading.value = true;
  try {
    const params: any = {
      id: currentCommentRecord.value.id,
    };

    if (commentType.value === 'audit') {
      // 技术审核
      params.auditContent = commentContent.value || '';
      params.auditStatus = 1;
    } else if (commentType.value === 'upAudit') {
      // 同意
      params.assignContent = commentContent.value || '';
      params.auditStatus = 2;
    }

    await defHttp.post({
      url: '/lims/employee/outTrainingAuditOrRollBack',
      params,
    });

    commentModalVisible.value = false;
    reload();
  } catch (error) {
    // 可以在这里添加错误处理
  } finally {
    commentLoading.value = false;
  }
}

/**
 * 意见取消
 */
function handleCommentCancel() {
  commentModalVisible.value = false;
  commentContent.value = '';
  currentCommentRecord.value = null;
}

/**
 * 成功回调
 */
function handleSuccess({ isUpdate, values }) {
  reload();
}
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '编辑',
      ifShow: () => {
        return record.auditStatus == '' || record.auditStatus == '1' || record.auditStatus == null || record.auditStatus == '99';
      },
      onClick: handleEdit.bind(null, record),
    },
  ];
}
/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  return [
    {
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
      },
      ifShow: () => {
        return record.status == '0';
      },
    },
    {
      label: '技术审核',
      auth: '一次审批',
      ifShow: () => {
        return record.auditStatus == '0';
      },
      onClick: handleAudit.bind(null, record),
    },
    {
      label: '驳回',
      auth: '一次审批驳回',
      ifShow: () => {
        return record.auditStatus == '0';
      },
      onClick: handleAuditReject.bind(null, record),
    },
    {
      label: '同意',
      auth: '二次审批',
      ifShow: () => {
        return record.auditStatus == '1';
      },
      onClick: handleUpAudit.bind(null, record),
    },
    {
      label: '驳回',
      auth: '二次审批驳回',
      ifShow: () => {
        return record.auditStatus == '1';
      },
      onClick: handleUpAuditReject.bind(null, record),
    },

    {
      label: '外出培训审批表',
      onClick: handleSeeSPB.bind(null, record),
    },
    {
      label: '上传证书',
      ifShow: () => {
        return record.auditStatus == '2';
      },
      onClick: handleUpload.bind(null, record),
    },
  ];
}
</script>
<style scoped></style>
