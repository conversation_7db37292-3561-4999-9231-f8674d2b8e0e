<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <!-- <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button> -->
        <!-- <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
        <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
<a-button>批量操作
  <Icon icon="mdi:chevron-down"></Icon>
</a-button>
</a-dropdown> -->
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>

    <!-- 表单区域 -->
    <externalTrainingModal ref="externalTrainingModalRef" @register="registerModal" @success="handleSuccess" @auditSuccess="handleAuditSuccess"></externalTrainingModal>
    <!-- 审批表打印 -->
    <SPBModal @register="registerSPBModal" @success="handleSuccess"></SPBModal>



    <!-- 上传证书弹窗 -->
    <a-modal v-model:visible="uploadModalVisible" title="上传证书" @ok="handleUploadConfirm" @cancel="handleUploadCancel"
      width="1000px" :confirm-loading="uploadLoading">
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="证书文件" :rules="[{ required: true, message: '请上传证书文件' }]">
          <j-upload v-model:value="certificateUrl" />
        </a-form-item>
      </a-form>
    </a-modal>


  </div>
</template>

<script lang="ts" name="userManagement-externalTraining-index" setup>
import { ref } from 'vue';
import { BasicTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { useListPage } from '/@/hooks/system/useListPage';
import externalTrainingModal from './modules/externalTrainingModal.vue';
import SPBModal from './modules/SPBModal.vue';
import { columns, searchFormSchema } from './externalTraining.data';
import { list, deleteOne, batchDelete, getImportUrl, getExportUrl, returnOne } from './externalTraining.api';
import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';

//注册model
const [registerModal, { openModal }] = useModal();
const [registerSPBModal, { openModal: openSPBModal }] = useModal();

// 获取externalTrainingModal的引用
const externalTrainingModalRef = ref();



// 上传证书弹窗相关状态
const uploadModalVisible = ref(false);
const certificateUrl = ref('');
const currentUploadRecord = ref(null);
const uploadLoading = ref(false); // 上传操作loading状态
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    title: '人员管理-外部培训',
    api: list,
    columns,
    canResize: false,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      fieldMapToTime: [['timeRange', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    },
    showActionColumn: true,
    showTableSetting: false,
    actionColumn: {
      width: 120,
    },
  },
  exportConfig: {
    name: '人员管理-外部培训',
    url: getExportUrl,
  },
  importConfig: {
    url: getImportUrl,
  },
});

const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

/**
 * 新增事件
 */
function handleAdd() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}
/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteOne({ id: record.id }, reload);
}
/**
 * 退回事件
 */
async function handleReturn(record) {
  await returnOne({ id: record.id }, reload);
}
/**
 * 人员外出培训审批表
 */
function handleSeeSPB(record) {
  openSPBModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  await batchDelete({ ids: selectedRowKeys.value }, reload);
}
/**
 * 技术审核（使用externalTrainingModal中的审核弹窗）
 */
function handleAudit(record) {
  if (externalTrainingModalRef.value) {
    externalTrainingModalRef.value.openAuditModal(record, 'audit');
  }
}

/**
 * 二次审批（使用externalTrainingModal中的审核弹窗）
 */
function handleUpAudit(record) {
  if (externalTrainingModalRef.value) {
    externalTrainingModalRef.value.openAuditModal(record, 'upAudit');
  }
}

/**
 * 审核成功回调
 */
function handleAuditSuccess() {
  reload();
}



/**
 * 上传证书
 */
function handleUpload(record) {
  currentUploadRecord.value = record;
  certificateUrl.value = record.certificateUrl;
  uploadModalVisible.value = true;
}

/**
 * 上传证书确认
 */
async function handleUploadConfirm() {
  if (!certificateUrl.value) {
    message.error('请上传证书文件');
    return;
  }

  if (uploadLoading.value) {
    return; // 防止重复提交
  }

  uploadLoading.value = true;
  try {
    await defHttp.put({
      url: '/lims/employee/uploadOutTrainingData',
      data: {
        id: currentUploadRecord.value.id,
        certificateUrl: certificateUrl.value,
      },
    });

    message.success('证书上传成功');
    uploadModalVisible.value = false;
    reload();
  } catch (error) {
    // message.error('证书上传失败');
  } finally {
    uploadLoading.value = false;
  }
}

/**
 * 上传证书取消
 */
function handleUploadCancel() {
  uploadModalVisible.value = false;
  certificateUrl.value = '';
  currentUploadRecord.value = null;
}



/**
 * 成功回调
 */
function handleSuccess() {
  reload();
}
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '编辑',
      ifShow: () => {
        return record.auditStatus == '' || record.auditStatus == '1' || record.auditStatus == null || record.auditStatus == '99';
      },
      onClick: handleEdit.bind(null, record),
    },
  ];
}
/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  return [
    {
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
      },
      ifShow: () => {
        return record.status == '0';
      },
    },
    {
      label: '技术审核',
      auth: '一次审批',
      ifShow: () => {
        return record.auditStatus == '0';
      },
      onClick: handleAudit.bind(null, record),
    },
    {
      label: '二次审批',
      auth: '二次审批',
      ifShow: () => {
        return record.auditStatus == '1';
      },
      onClick: handleUpAudit.bind(null, record),
    },

    {
      label: '外出培训审批表',
      onClick: handleSeeSPB.bind(null, record),
    },
    {
      label: '上传证书',
      ifShow: () => {
        return record.auditStatus == '2';
      },
      onClick: handleUpload.bind(null, record),
    },
  ];
}
</script>
<style scoped></style>
