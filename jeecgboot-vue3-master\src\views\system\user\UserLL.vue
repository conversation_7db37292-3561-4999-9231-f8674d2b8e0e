<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="员工培训履历表" @ok="handleSubmit" okText="确认" :width="1400">
    <div class="table-container">
      <div class="table-header">
        <div class="table-actions">
          <button class="action-btn" @click="printTable">打印</button>
        </div>
      </div>
      <table :id="printId" border="1" cellspacing="0" cellpadding="5" style="width: 100%; border-collapse: collapse">
        <thead>
          <tr>
            <th style="text-align: center" colspan="6">员工培训履历表</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th width="20%">姓名</th>
            <td width="20%">{{ formData.name }}</td>
            <th width="20%">岗位</th>
            <td width="40%" colspan="2">{{ formData.position }}</td>
          </tr>
          <tr>
            <th>培训周期</th>
            <th>培训内容</th>
            <th>培训单位/教师</th>
            <th>培训成绩</th>
            <th>备注</th>
          </tr>
          <tr v-for="(item, index) in formData.trainingRecords" :key="index">
            <td>{{ item.period }}</td>
            <td>{{ item.content }}</td>
            <td>{{ item.instructor }}</td>
            <td>{{ item.score }}</td>
            <td>{{ item.remarks }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </BasicModal>
</template>
<script lang="ts" name="UserJLModal" setup>
import { ref, computed, unref, reactive } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { message } from 'ant-design-vue';
import printJS from 'print-js';
import { buildUUID } from '/@/utils/uuid';
import { defHttp1 } from '/@/utils/http/axios/index1';
import dayjs from 'dayjs';

const printId = ref('');

// 声明Emits
const emit = defineEmits(['success', 'register']);

// 定义表单数据
const formData = reactive<any>({
  name: '',
  position: '',
  trainingRecords: [],
});

//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  defHttp1.get({ url: '/lims/employee/getOutTraining', params: { userCode: data.record.username } }).then((res) => {
    console.log("🚀 ~ res:", res.result)
    formData.name = data.record.realname || '';
    formData.position = res.result.position || '';
    if (res.result) {
      // 如果有传入数据，则赋值
      formData.trainingRecords = res.result.trainingDOS.map((item) => ({
        period: dayjs(item.startTime).format('YYYY-MM-DD') + '~' +dayjs(item.endTime).format('YYYY-MM-DD') ,
        content: item.trainReason,
        instructor: item.organizingUnit,
        score: item.score,
        remarks: '',
      }))
    }
  });
  printId.value = buildUUID().toString();

});

// 打印表格
function printTable() {
  printJS({
    type: 'html',
    printable: printId.value,
    scanStyles: false,
  });
}

async function handleSubmit() {
  closeModal();
}
</script>

<style scoped>
.table-container {
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  text-align: center;
  font-weight: bold;
  font-size: 18px;
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 5px 10px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.action-btn:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

table {
  border: 1px solid #ccc;
  width: 100%;
}

th,
td {
  border: 1px solid #ccc;
  text-align: center;
  padding: 8px;
  height: 40px;
}

th {
  background-color: #f2f2f2;
  font-weight: bold;
}
</style>
