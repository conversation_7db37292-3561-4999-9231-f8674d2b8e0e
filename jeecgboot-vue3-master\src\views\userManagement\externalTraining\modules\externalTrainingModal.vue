<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="60%">
    <a-form ref="formRef" :model="orderMainModel" @submit="handleSubmit" :label-col="labelCol" :wrapper-col="wrapperCol"
      :rules="validatorRules">

      <a-row class="form-row" :gutter="8">
        <a-col :span="20">
          <a-form-item label="培训班名称" name="className">
            <a-input v-model:value="orderMainModel.className" placeholder="请输入培训班名称" />
          </a-form-item>
        </a-col>
        
        <a-col :span="20">
          <a-form-item label="外派培训原因" name="trainReason">
            <a-input v-model:value="orderMainModel.trainReason" placeholder="请输入外派培训原因" />
          </a-form-item>
        </a-col>
        
        <a-col :span="20">
          <a-form-item label="培训目的" name="trainPurpose">
            <a-input v-model:value="orderMainModel.trainPurpose" placeholder="请输入培训目的" />
          </a-form-item>
        </a-col>
        
        <a-col :span="20">
          <a-form-item label="培训地点" name="trainPlace">
            <a-input v-model:value="orderMainModel.trainPlace" placeholder="请输入培训地点" />
          </a-form-item>
        </a-col>
        
        <a-col :span="20">
          <a-form-item label="举办单位" name="organizingUnit">
            <a-input v-model:value="orderMainModel.organizingUnit" placeholder="请输入举办单位" />
          </a-form-item>
        </a-col>
        
        <a-col :span="20">
          <a-form-item label="实际课时总数" name="trainNumber">
            <a-input v-model:value="orderMainModel.trainNumber" placeholder="请输入实际课时总数" />
          </a-form-item>
        </a-col>
        
        <a-col :span="20">
          <a-form-item label="预算费用" name="estimatedCost">
            <a-input v-model:value="orderMainModel.estimatedCost" placeholder="请输入预算费用" />
          </a-form-item>
        </a-col>
        
        <a-col :span="20">
          <a-form-item label="实际费用" name="realCost">
            <a-input v-model:value="orderMainModel.realCost" placeholder="请输入实际费用" />
          </a-form-item>
        </a-col>
        
        <a-col :span="20">
          <a-form-item label="有无相关证书" name="relatedCertificates">
            <a-radio-group v-model:value="orderMainModel.relatedCertificates">
              <a-radio value="有">有</a-radio>
              <a-radio value="无">无</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        
        <a-col :span="20">
          <a-form-item label="部门所属体系" name="departmentSystem">
            <a-select v-model:value="orderMainModel.departmentSystem" placeholder="请选择部门所属体系">
              <a-select-option value="供应链体系">供应链体系</a-select-option>
              <a-select-option value="产品开发中心">产品开发中心</a-select-option>
              <a-select-option value="ODM业务部">ODM业务部</a-select-option>
              <a-select-option value="日化事业部">日化事业部</a-select-option>
              <a-select-option value="电商生态事业部">电商生态事业部</a-select-option>
              <a-select-option value="其他部门">其他部门</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        
        <a-col :span="20">
          <a-form-item label="起止时间" name="duration">
            <a-range-picker v-model:value="orderMainModel.duration" @change="changeDuration" />
          </a-form-item>
        </a-col>
        

        
        <!-- 文件上传字段 -->
        <a-col :span="20">
          <a-form-item label="预算费用明细" name="estimatedCostUrl">
            <j-upload v-model:value="orderMainModel.estimatedCostUrl" :fileMaxSize="50" :fileMaxNumber="10" />
          </a-form-item>
        </a-col>
        
        <a-col :span="20">
          <a-form-item label="费用明细" name="costDetailUrl">
            <j-upload v-model:value="orderMainModel.costDetailUrl" :fileMaxSize="50" :fileMaxNumber="10" />
          </a-form-item>
        </a-col>
        
        <a-col :span="20">
          <a-form-item label="培训课程内容记录" name="recordUrl">
            <j-upload v-model:value="orderMainModel.recordUrl" :fileMaxSize="50" :fileMaxNumber="10" />
          </a-form-item>
        </a-col>
        
        <a-col :span="20">
          <a-form-item label="培训心得体会" name="experienceUrl">
            <j-upload v-model:value="orderMainModel.experienceUrl" :fileMaxSize="50" :fileMaxNumber="10" />
          </a-form-item>
        </a-col>
        
        <a-col :span="20">
          <a-form-item label="授课老师评估/ 培训服务机构评估" name="assessmentUrl">
            <j-upload v-model:value="orderMainModel.assessmentUrl" :fileMaxSize="50" :fileMaxNumber="10" />
          </a-form-item>
        </a-col>
        
        <a-col :span="20" v-show="false">
          <a-form-item label="人员工号" name="userCode">
            <a-input disabled v-model:value="orderMainModel.userCode" placeholder="请输入人员工号" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="人员名称" name="userName">
            <a-input :class="{ fontColor: true }" disabled v-model:value="orderMainModel.userName" placeholder="请输入人员名称" />
          </a-form-item>
        </a-col>
        <a-col :span="2">
          <a-button preIcon="ant-design:user-switch-outlined" @click="addPerson">添加人员</a-button>
        </a-col>
      </a-row>
    </a-form>
    <ChoosePersonModal @register="cpModal" @success="handleCpReturn"></ChoosePersonModal>
  </BasicModal>

  <!-- 审核弹窗 -->
  <BasicModal v-bind="$attrs" @register="registerAuditModal" :title="auditModalTitle" @ok="handleAuditConfirm" width="80%">
    <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="培训班名称" name="className">
            <a-input v-model:value="currentAuditRecord.className" placeholder="请输入培训班名称" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="外派培训原因" name="trainReason">
            <a-input v-model:value="currentAuditRecord.trainReason" placeholder="请输入外派培训原因" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="培训目的" name="trainPurpose">
            <a-input v-model:value="currentAuditRecord.trainPurpose" placeholder="请输入培训目的" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="培训地点" name="trainPlace">
            <a-input v-model:value="currentAuditRecord.trainPlace" placeholder="请输入培训地点" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="举办单位" name="organizingUnit">
            <a-input v-model:value="currentAuditRecord.organizingUnit" placeholder="请输入举办单位" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="实际课时总数" name="trainNumber">
            <a-input v-model:value="currentAuditRecord.trainNumber" placeholder="请输入实际课时总数" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="预算费用" name="estimatedCost">
            <a-input v-model:value="currentAuditRecord.estimatedCost" placeholder="请输入预算费用" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="实际费用" name="realCost">
            <a-input v-model:value="currentAuditRecord.realCost" placeholder="请输入实际费用" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="有无相关证书" name="relatedCertificates">
            <a-radio-group v-model:value="currentAuditRecord.relatedCertificates" disabled>
              <a-radio value="有">有</a-radio>
              <a-radio value="无">无</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="部门所属体系" name="departmentSystem">
            <a-select v-model:value="currentAuditRecord.departmentSystem" placeholder="请选择部门所属体系" disabled>
              <a-select-option value="供应链体系">供应链体系</a-select-option>
              <a-select-option value="产品开发中心">产品开发中心</a-select-option>
              <a-select-option value="ODM业务部">ODM业务部</a-select-option>
              <a-select-option value="日化事业部">日化事业部</a-select-option>
              <a-select-option value="电商生态事业部">电商生态事业部</a-select-option>
              <a-select-option value="其他部门">其他部门</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="开始时间" name="startTime">
            <a-input v-model:value="currentAuditRecord.startTime" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="结束时间" name="endTime">
            <a-input v-model:value="currentAuditRecord.endTime" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="人员工号" name="userCode">
            <a-input disabled v-model:value="currentAuditRecord.userCode" placeholder="请输入人员工号" />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="人员名称" name="userName">
            <a-input disabled v-model:value="currentAuditRecord.userName" placeholder="请输入人员名称" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 文件上传字段 -->
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="预算费用明细" name="estimatedCostUrl">
            <j-upload v-model:value="currentAuditRecord.estimatedCostUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="费用明细" name="costDetailUrl">
            <j-upload v-model:value="currentAuditRecord.costDetailUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="培训课程内容记录" name="recordUrl">
            <j-upload v-model:value="currentAuditRecord.recordUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="培训心得体会" name="experienceUrl">
            <j-upload v-model:value="currentAuditRecord.experienceUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="授课老师评估/ 培训服务机构评估" name="assessmentUrl">
            <j-upload v-model:value="currentAuditRecord.assessmentUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 历史审核意见 -->
      <a-row :gutter="16" v-if="currentAuditRecord && (currentAuditRecord.auditContent || currentAuditRecord.assignPerson || currentAuditRecord.rejectReason)">
        <a-col :span="24">
          <a-divider>历史审核意见</a-divider>
        </a-col>

        <a-col :span="24" v-if="currentAuditRecord.auditContent">
          <a-form-item label="技术负责人意见" name="auditContent">
            <a-textarea :rows="3" disabled v-model:value="currentAuditRecord.auditContent" />
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="currentAuditRecord.assignPerson">
          <a-form-item label="实验室经理意见" name="assignPerson">
            <a-textarea :rows="3" disabled v-model:value="currentAuditRecord.assignPerson" />
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="currentAuditRecord.rejectReason">
          <a-form-item label="驳回原因" name="rejectReason">
            <a-textarea :rows="3" disabled v-model:value="currentAuditRecord.rejectReason" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 审核操作区域 -->
      <a-row :gutter="16">
        <a-col :span="24">
          <a-divider>审核操作</a-divider>
        </a-col>

        <a-col :span="24">
          <a-form-item label="操作类型" :rules="[{ required: true, message: '请选择操作类型' }]">
            <a-radio-group v-model:value="auditActionType">
              <a-radio value="approve">同意</a-radio>
              <a-radio value="reject">驳回</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>

        <!-- 同意时显示意见输入框 -->
        <a-col :span="24" v-if="auditActionType === 'approve'">
          <a-form-item :label="auditType === 'audit' ? '技术审核意见' : '审批意见'"
            :rules="[{ required: true, message: '请输入意见内容' }]">
            <a-textarea v-model:value="auditContent" placeholder="请输入意见内容" :rows="4" :maxlength="200" show-count />
          </a-form-item>
        </a-col>

        <!-- 驳回时显示驳回理由输入框 -->
        <a-col :span="24" v-if="auditActionType === 'reject'">
          <a-form-item label="驳回理由" :rules="[{ required: true, message: '请输入驳回理由' }]">
            <a-textarea v-model:value="rejectReason" placeholder="请输入驳回理由" :rows="4" :maxlength="200" show-count />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, unref } from 'vue';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { getToken } from '/@/utils/auth';
import { saveOrUpdate } from '../externalTraining.api';
import ChoosePersonModal from '/@/views/reagent/modules/ChoosePersonModal.vue'
import { message, Upload } from 'ant-design-vue';
import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
import dayjs from 'dayjs';
import { defHttp } from '/@/utils/http/axios';

// 移除了不必要的类型别名声明

// Emits声明
const emit = defineEmits(['register', 'success', 'auditSuccess']);
const formRef = ref();
const isUpdate = ref(true);

// 审核弹窗相关状态
const auditModalTitle = ref('');
const currentAuditRecord = ref<any>({});
const auditType = ref(''); // 'audit' 或 'upAudit'
const auditActionType = ref(''); // 'approve' 或 'reject'
const auditContent = ref(''); // 同意时的意见内容
const rejectReason = ref(''); // 驳回时的理由
const auditLoading = ref(false); // 审核操作loading状态
const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 5 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 16 },
});

interface RecordFile {
  id: string;
  requireVal: string;
  fileList: any[];
}

const orderMainModel = reactive<any>({
  id: null,
  userCode: '',
  userName: '',
  className: '',
  startTime: '',
  endTime: '',
  organizingUnit: '',
  trainReason: '',
  duration: [],
  recordFile: [] as RecordFile[],
  // 新增字段
  trainPurpose: '',
  trainNumber: '',
  trainPlace: '',
  relatedCertificates: '',
  estimatedCost: '',
  realCost: '',
  departmentSystem: '',
  estimatedCostUrl: '',
  costDetailUrl: '',
  recordUrl: '',
  experienceUrl: '',
  assessmentUrl: '',
});
const headers = reactive({
  'X-Access-Token': getToken(),
});
let site = ref(0);
let fileList = ref([]);
const validatorRules = {
  userCode: [{ required: true, message: '人员工号必填！' }],
  userName: [{ required: true, message: '人员名称必填！' }],
  className: [{ required: true, message: '培训班名称必填！' }],
  estimatedCost: [{ required: true, message: '预算费用必填！' }],
  duration: [{ required: true, message: '时间必选！' }],
  startTime: [{ required: true, message: '开始时间必填！' }],
  endTime: [{ required: true, message: '结束时间必填！' }],
  organizingUnit: [{ required: true, message: '举办单位必填！' }],
  trainReason: [{ required: true, message: '培训内容必填！' },],
  trainPlace: [{ required: true, message: '培训地点必填！' }],
  trainNumber: [{ required: true, message: '实际课时总数必填！' }],
  relatedCertificates: [{ required: true, message: '请选择有无相关证书！' }],
  departmentSystem: [{ required: true, message: '部门所属体系必填！' }],
};
//表单配置
// const [registerForm, {resetFields, setFieldsValue, validate}] = useForm({
//     labelWidth: 150,
//     schemas: formSchema,
//     showActionButtonGroup: false,
// });
//表单赋值
const [cpModal, { openModal: choosePModal }] = useModal();
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  formRef.value?.resetFields();
  reset();
  setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  if (unref(isUpdate)) {
    Object.assign(orderMainModel, data.record);
    orderMainModel.duration = [dayjs(orderMainModel.startTime), dayjs(orderMainModel.endTime)];
    console.log("🚀 ~ orderMainModel:", orderMainModel)
  } else {
  }
});

// 审核弹窗注册
const [registerAuditModal, { openModal: openAuditModalInner, closeModal: closeAuditModal }] = useModal();
//设置标题
const title = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));
//表单提交事件
function handleSubmit(v) {
  formRef.value
    ?.validate()
    .then(async () => {
      try {
        // let values = await validate();
        orderMainModel.startTime = dayjs(orderMainModel.duration[0]).format('YYYY-MM-DD');
        orderMainModel.endTime = dayjs(orderMainModel.duration[1]).format('YYYY-MM-DD');
        setModalProps({ confirmLoading: true });
        //提交表单
        await saveOrUpdate(orderMainModel, isUpdate.value);
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, orderMainModel });
      } finally {
        setModalProps({ confirmLoading: false });
      }
    })
    .catch((error: any) => {
      console.log('error', error);
    });
}
function reset() {
  orderMainModel.id = null;
  orderMainModel.userCode = '';
  orderMainModel.userName = '';
  orderMainModel.className = '';
  orderMainModel.startTime = '';
  orderMainModel.endTime = '';
  orderMainModel.organizingUnit = '';
  orderMainModel.trainReason = '';
  orderMainModel.duration = [];
  orderMainModel.recordFile = [];
  site.value = 0;
  // orderMainModel.peopleNumber = '';
  // 重置新增字段
  orderMainModel.trainPurpose = '';
  orderMainModel.trainNumber = '';
  orderMainModel.trainPlace = '';
  orderMainModel.relatedCertificates = '';
  orderMainModel.estimatedCost = '';
  orderMainModel.realCost = '';
  orderMainModel.departmentSystem = '';
  orderMainModel.estimatedCostUrl = '';
  orderMainModel.costDetailUrl = '';
  orderMainModel.recordUrl = '';
  orderMainModel.experienceUrl = '';
  orderMainModel.assessmentUrl = '';
}
function changeDuration(dates) {
  if (dates && dates.length === 2) {
    orderMainModel.startTime = dayjs(dates[0]).format('YYYY-MM-DD');
    orderMainModel.endTime = dayjs(dates[1]).format('YYYY-MM-DD');
  } else {
    orderMainModel.startTime = '';
    orderMainModel.endTime = '';
  }
  console.log("🚀 ~ changeDuration ~ orderMainModel:", orderMainModel)
}

/**
 * 审核确认
 */
async function handleAuditConfirm() {
  // 验证必填字段
  if (!auditActionType.value) {
    message.error('请选择操作类型');
    return;
  }

  if (auditActionType.value === 'approve' && !auditContent.value.trim()) {
    message.error('请输入意见内容');
    return;
  }

  if (auditActionType.value === 'reject' && !rejectReason.value.trim()) {
    message.error('请输入驳回理由');
    return;
  }

  if (auditLoading.value) {
    return; // 防止重复提交
  }

  auditLoading.value = true;
  try {
    const params: any = {
      id: currentAuditRecord.value.id,
    };

    if (auditActionType.value === 'approve') {
      // 同意操作
      if (auditType.value === 'audit') {
        // 技术审核同意
        params.auditContent = auditContent.value;
        params.auditStatus = 1;
      } else if (auditType.value === 'upAudit') {
        // 二次审批同意
        params.assignContent = auditContent.value;
        params.auditStatus = 2;
      }
    } else if (auditActionType.value === 'reject') {
      // 驳回操作
      if (auditType.value === 'audit') {
        // 技术审核驳回
        params.rejectReason = rejectReason.value;
        params.auditStatus = 99;
      } else if (auditType.value === 'upAudit') {
        // 二次审批驳回
        params.auditContent = rejectReason.value;
        params.auditStatus = 99;
      }
    }

    await defHttp.post({
      url: '/lims/employee/outTrainingAuditOrRollBack',
      params,
    });

    message.success(auditActionType.value === 'approve' ? '审核通过' : '驳回成功');
    closeAuditModal();
    emit('auditSuccess');
  } catch (error) {
    // 可以在这里添加错误处理
  } finally {
    auditLoading.value = false;
  }
}

function addPerson() {
  choosePModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}

function handleCpReturn(source) {
  console.log("🚀 ~ file: PlanDevelopmentModal.vue:163 ~ handleCpReturn ~ source:", source)

  if (source.length != 0) {
    orderMainModel.userCode = '';
    orderMainModel.userName = '';

    for (let i = 0; i < source.length; i++) {
      orderMainModel.userCode += source[i].username;
      orderMainModel.userName += source[i].realname;

      if (i + 1 != source.length) {
        orderMainModel.userCode += ',';
        orderMainModel.userName += ',';
      }
    }
  }

}

function addSight() {
  let curId = 'z' + site.value;
  site.value++;
  orderMainModel.recordFile.push({
    id: curId,
    requireVal: '',
    fileList: []
  })
}
function removeSight(sight) {
  console.log("🚀 ~ file: PlanDevelopmentModal.vue:222 ~ removeSight ~ sight:", sight)
  orderMainModel.recordFile = orderMainModel.recordFile.filter((item) => item.id !== sight.id);
}

const handleChange = (info: any) => {
  console.log("🚀 ~ file: PlanProcessModal.vue:271 ~ handleChange ~ info:", info)
  if (info.file.status !== 'uploading') {
    console.log(info.file, info.fileList);
  }
  if (info.file.status === 'done') {
    message.success(`${info.file.name} file uploaded successfully`);
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} file upload failed.`);
  }
};

// 预览文件、图片
function onFilePreview(file) {
  // if (isImageMode.value) {
  //   createImgPreview({ imageList: [file.url], maskClosable: true });
  // } else {

  // }

  window.open(file.url);
}
const beforeUpload = (file: any) => {
  console.log("🚀 ~ file: PlanProcessModal.vue:302 ~ file:", file)
  const isPDf = file.type === 'application/pdf';
  if (!isPDf) {
    message.error(`${file.name} 不是一个pdf文件`);
  }
  console.log('', orderMainModel.recordFile)
  return isPDf || Upload.LIST_IGNORE;
};

// 暴露审核弹窗打开函数
function openAuditModal(record: any, type: 'audit' | 'upAudit') {
  currentAuditRecord.value = { ...record };
  auditType.value = type;
  auditModalTitle.value = type === 'audit' ? '技术审核' : '二次审批';
  auditActionType.value = '';
  auditContent.value = '';
  rejectReason.value = '';
  openAuditModalInner(true);
}

// 暴露给父组件使用的函数
defineExpose({
  openAuditModal,
  registerAuditModal
});
</script>

<style lang="less" scoped>
.fontColor {
  color: black;
}
</style>