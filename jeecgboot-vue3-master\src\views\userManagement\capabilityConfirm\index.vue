<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
        <!-- <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
        <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
<a-button>批量操作
  <Icon icon="mdi:chevron-down"></Icon>
</a-button>
</a-dropdown> -->
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>

    <!-- 表单区域 -->
    <capabilityConfirmModal ref="capabilityConfirmModalRef" @register="registerModal" @success="handleSuccess" @auditSuccess="handleAuditSuccess"></capabilityConfirmModal>
    <capabilityConfirmTableModal @register="registerCapabilityConfirmTableModal" @success="handleSuccess">
    </capabilityConfirmTableModal>


  </div>
</template>

<script lang="ts" name="assContent" setup>
import { ref } from 'vue';
import { BasicTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { useListPage } from '/@/hooks/system/useListPage';
import capabilityConfirmModal from './modules/capabilityConfirmModal.vue';
import capabilityConfirmTableModal from './modules/capabilityConfirmTableModal.vue';
import { columns, searchFormSchema } from './capabilityConfirm.data';
import { list, batchDelete, getImportUrl, getExportUrl, returnOne } from './capabilityConfirm.api';
import { defHttp } from '/@/utils/http/axios';

//注册model
const [registerModal, { openModal }] = useModal();
const [registerCapabilityConfirmTableModal, { openModal: openCapabilityConfirmTableModal }] = useModal();

// 获取capabilityConfirmModal的引用
const capabilityConfirmModalRef = ref();


//注册table数据
const { tableContext, onExportXls } = useListPage({
  tableProps: {
    title: '人员管理-人员考核内容汇总表',
    api: list,
    columns,
    canResize: false,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      // fieldMapToTime: [['timeRange', ['planStartTime', 'planEndTime'], 'YYYY-MM-DD']],
    },
    showTableSetting: false,
    showIndexColumn: true,
    actionColumn: {
      width: 120,
    },
  },
  exportConfig: {
    name: '人员管理-人员考核内容汇总表',
    url: getExportUrl,
  },
  importConfig: {
    url: getImportUrl,
  },
});

const [registerTable, { reload }, { selectedRowKeys }] = tableContext;

/**
 * 新增事件
 */
function handleAdd() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}
/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 详情
 */
function handleDetailTable(record: Recordable) {
  openCapabilityConfirmTableModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
/**
 * 提交
 */
async function handleSubmit(record) {
  await defHttp.post({
    url: '/lims/employee/onDutyAbilityCommit',
    params: { id: record.id },
  });
  reload();
}
/**
 * 技术审核（使用capabilityConfirmModal中的审核弹窗）
 */
function handleAudit(record) {
  if (capabilityConfirmModalRef.value) {
    capabilityConfirmModalRef.value.openAuditModal(record, 'audit');
  }
}

/**
 * 二次审批（使用capabilityConfirmModal中的审核弹窗）
 */
function handleUpAudit(record) {
  if (capabilityConfirmModalRef.value) {
    capabilityConfirmModalRef.value.openAuditModal(record, 'upAudit');
  }
}

/**
 * 提交确认（使用capabilityConfirmModal中的审核弹窗）
 */
function handleConfirm(record) {
  if (capabilityConfirmModalRef.value) {
    capabilityConfirmModalRef.value.openAuditModal(record, 'confirm');
  }
}

/**
 * 审核成功回调
 */
function handleAuditSuccess() {
  reload();
}



/**
 * 退回事件
 */
async function handleReturn(record) {
  await returnOne({ id: record.id }, reload);
}
/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  await batchDelete({ ids: selectedRowKeys.value }, reload);
}
/**
 * 成功回调
 */
function handleSuccess() {
  reload();
}
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record),
    },
  ];
}
/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  return [
    // {
    //   label: '详情',
    //   onClick: handleDetail.bind(null, record),
    // },
    {
      label: '提交',
      onClick: handleConfirm.bind(null, record),
      ifShow: () => {
        return record.auditStatus == null || record.auditStatus == '' || record.auditStatus == 99;
      },
    },
    {
      label: '二次审批',
      ifShow: () => {
        return record.auditStatus == '1';
      },
      onClick: handleUpAudit.bind(null, record),
    },
    {
      label: '技术审核',
      ifShow: () => {
        return record.auditStatus == '0';
      },
      onClick: handleAudit.bind(null, record),
    },
    {
      label: '人员上岗能力确认记录',
      ifShow: () => {
        return record.auditStatus == '2';
      },
      onClick: handleDetailTable.bind(null, record),
    },
    // {
    //   label: '退回',
    //   popConfirm: {
    //     title: '是否确认退回',
    //     confirm: handleReturn.bind(null, record),
    //   },
    //   ifShow: () => {
    //     return record.status == '0';
    //   },
    // },
  ];
}
</script>
<style scoped></style>