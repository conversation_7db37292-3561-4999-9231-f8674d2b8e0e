<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <!-- <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button> -->
        <!-- <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
        <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
<a-button>批量操作
  <Icon icon="mdi:chevron-down"></Icon>
</a-button>
</a-dropdown> -->
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>

    <!-- 表单区域 -->
    <externalTrainingModal @register="registerModal" @success="handleSuccess"></externalTrainingModal>
    <!-- 审批表打印 -->
    <SPBModal @register="registerSPBModal" @success="handleSuccess"></SPBModal>

    <!-- 审核弹窗 -->
    <a-modal v-model:visible="auditModalVisible" :title="auditModalTitle" @ok="handleAuditConfirm"
      @cancel="handleAuditCancel" width="1000px" :confirm-loading="auditLoading">
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="培训班名称" name="className">
          <a-input v-model:value="currentAuditRecord.className" placeholder="请输入培训班名称" disabled />
        </a-form-item>

        <a-form-item label="外派培训原因" name="trainReason">
          <a-input v-model:value="currentAuditRecord.trainReason" placeholder="请输入外派培训原因" disabled />
        </a-form-item>

        <a-form-item label="培训目的" name="trainPurpose">
          <a-input v-model:value="currentAuditRecord.trainPurpose" placeholder="请输入培训目的" disabled />
        </a-form-item>

        <a-form-item label="培训地点" name="trainPlace">
          <a-input v-model:value="currentAuditRecord.trainPlace" placeholder="请输入培训地点" disabled />
        </a-form-item>

        <a-form-item label="举办单位" name="organizingUnit">
          <a-input v-model:value="currentAuditRecord.organizingUnit" placeholder="请输入举办单位" disabled />
        </a-form-item>

        <a-form-item label="实际课时总数" name="trainNumber">
          <a-input v-model:value="currentAuditRecord.trainNumber" placeholder="请输入实际课时总数" disabled />
        </a-form-item>

        <a-form-item label="预算费用" name="estimatedCost">
          <a-input v-model:value="currentAuditRecord.estimatedCost" placeholder="请输入预算费用" disabled />
        </a-form-item>

        <a-form-item label="实际费用" name="realCost">
          <a-input v-model:value="currentAuditRecord.realCost" placeholder="请输入实际费用" disabled />
        </a-form-item>

        <a-form-item label="有无相关证书" name="relatedCertificates">
          <a-radio-group v-model:value="currentAuditRecord.relatedCertificates" disabled>
            <a-radio value="有">有</a-radio>
            <a-radio value="无">无</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="部门所属体系" name="departmentSystem">
          <a-select v-model:value="currentAuditRecord.departmentSystem" placeholder="请选择部门所属体系" disabled>
            <a-select-option value="供应链体系">供应链体系</a-select-option>
            <a-select-option value="产品开发中心">产品开发中心</a-select-option>
            <a-select-option value="ODM业务部">ODM业务部</a-select-option>
            <a-select-option value="日化事业部">日化事业部</a-select-option>
            <a-select-option value="电商生态事业部">电商生态事业部</a-select-option>
            <a-select-option value="其他部门">其他部门</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="开始时间" name="startTime">
          <a-input v-model:value="currentAuditRecord.startTime" disabled />
        </a-form-item>
        <a-form-item label="结束时间" name="endTime">
          <a-input v-model:value="currentAuditRecord.endTime" disabled />
        </a-form-item>

        <a-form-item label="预算费用明细" name="estimatedCostUrl">
          <j-upload v-model:value="currentAuditRecord.estimatedCostUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
        </a-form-item>

        <a-form-item label="费用明细" name="costDetailUrl">
          <j-upload v-model:value="currentAuditRecord.costDetailUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
        </a-form-item>

        <a-form-item label="培训课程内容记录" name="recordUrl">
          <j-upload v-model:value="currentAuditRecord.recordUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
        </a-form-item>

        <a-form-item label="培训心得体会" name="experienceUrl">
          <j-upload v-model:value="currentAuditRecord.experienceUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
        </a-form-item>

        <a-form-item label="授课老师评估/ 培训服务机构评估" name="assessmentUrl">
          <j-upload v-model:value="currentAuditRecord.assessmentUrl" :fileMaxSize="50" :fileMaxNumber="10" disabled />
        </a-form-item>

        <a-form-item label="人员工号" name="userCode">
          <a-input disabled v-model:value="currentAuditRecord.userCode" placeholder="请输入人员工号" />
        </a-form-item>
        <a-form-item label="人员名称" name="userName">
          <a-input :class="{ fontColor: true }" disabled v-model:value="currentAuditRecord.userName"
            placeholder="请输入人员名称" />
        </a-form-item>
        <a-form-item label="技术负责人意见" name="auditContent" v-if='currentAuditRecord && currentAuditRecord.auditContent'>
          <a-textarea :rows="4" disabled v-model:value="currentAuditRecord.auditContent" />
        </a-form-item>
        <a-form-item label="实验室经理意见" name="assignPerson" v-if='currentAuditRecord && currentAuditRecord.assignPerson'>
          <a-textarea :rows="4" disabled v-model:value="currentAuditRecord.assignPerson" />
        </a-form-item>
        <a-form-item label="驳回原因" name="rejectReason" v-if='currentAuditRecord && currentAuditRecord.rejectReason'>
          <a-textarea :rows="4" disabled v-model:value="currentAuditRecord.rejectReason" />
        </a-form-item>

        <!-- 操作类型选择 -->
        <a-form-item label="操作类型" :rules="[{ required: true, message: '请选择操作类型' }]">
          <a-radio-group v-model:value="auditActionType">
            <a-radio value="approve">同意</a-radio>
            <a-radio value="reject">驳回</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 同意时显示意见输入框 -->
        <a-form-item v-if="auditActionType === 'approve'" :label="auditType === 'audit' ? '技术审核意见' : '审批意见'"
          :rules="[{ required: true, message: '请输入意见内容' }]">
          <a-textarea v-model:value="auditContent" placeholder="请输入意见内容" :rows="4" :maxlength="200" show-count />
        </a-form-item>

        <!-- 驳回时显示驳回理由输入框 -->
        <a-form-item v-if="auditActionType === 'reject'" label="驳回理由"
          :rules="[{ required: true, message: '请输入驳回理由' }]">
          <a-textarea v-model:value="rejectReason" placeholder="请输入驳回理由" :rows="4" :maxlength="200" show-count />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 上传证书弹窗 -->
    <a-modal v-model:visible="uploadModalVisible" title="上传证书" @ok="handleUploadConfirm" @cancel="handleUploadCancel"
      width="1000px" :confirm-loading="uploadLoading">
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="证书文件" :rules="[{ required: true, message: '请上传证书文件' }]">
          <j-upload v-model:value="certificateUrl" />
        </a-form-item>
      </a-form>
    </a-modal>


  </div>
</template>

<script lang="ts" name="userManagement-externalTraining-index" setup>
import { ref } from 'vue';
import { BasicTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { useListPage } from '/@/hooks/system/useListPage';
import externalTrainingModal from './modules/externalTrainingModal.vue';
import SPBModal from './modules/SPBModal.vue';
import { columns, searchFormSchema } from './externalTraining.data';
import { list, deleteOne, batchDelete, getImportUrl, getExportUrl, returnOne } from './externalTraining.api';
import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';

//注册model
const [registerModal, { openModal }] = useModal();
const [registerSPBModal, { openModal: openSPBModal }] = useModal();

// 审核弹窗相关状态
const auditModalVisible = ref(false);
const auditModalTitle = ref('');
const currentAuditRecord = ref<any>({});
const auditType = ref(''); // 'audit' 或 'upAudit'
const auditActionType = ref(''); // 'approve' 或 'reject'
const auditContent = ref(''); // 同意时的意见内容
const rejectReason = ref(''); // 驳回时的理由
const auditLoading = ref(false); // 审核操作loading状态

// 上传证书弹窗相关状态
const uploadModalVisible = ref(false);
const certificateUrl = ref('');
const currentUploadRecord = ref(null);
const uploadLoading = ref(false); // 上传操作loading状态
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    title: '人员管理-外部培训',
    api: list,
    columns,
    canResize: false,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      fieldMapToTime: [['timeRange', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    },
    showActionColumn: true,
    showTableSetting: false,
    actionColumn: {
      width: 120,
    },
  },
  exportConfig: {
    name: '人员管理-外部培训',
    url: getExportUrl,
  },
  importConfig: {
    url: getImportUrl,
  },
});

const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

/**
 * 新增事件
 */
function handleAdd() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}
/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteOne({ id: record.id }, reload);
}
/**
 * 退回事件
 */
async function handleReturn(record) {
  await returnOne({ id: record.id }, reload);
}
/**
 * 人员外出培训审批表
 */
function handleSeeSPB(record) {
  openSPBModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  await batchDelete({ ids: selectedRowKeys.value }, reload);
}
/**
 * 技术审核（合并同意和驳回）
 */
function handleAudit(record) {
  currentAuditRecord.value = { ...record };
  auditType.value = 'audit';
  auditModalTitle.value = '技术审核';
  auditActionType.value = '';
  auditContent.value = '';
  rejectReason.value = '';
  auditModalVisible.value = true;
}

/**
 * 二次审批（合并同意和驳回）
 */
function handleUpAudit(record) {
  currentAuditRecord.value = { ...record };
  auditType.value = 'upAudit';
  auditModalTitle.value = '二次审批';
  auditActionType.value = '';
  auditContent.value = '';
  rejectReason.value = '';
  auditModalVisible.value = true;
}

/**
 * 审核确认
 */
async function handleAuditConfirm() {
  // 验证必填字段
  if (!auditActionType.value) {
    message.error('请选择操作类型');
    return;
  }

  if (auditActionType.value === 'approve' && !auditContent.value.trim()) {
    message.error('请输入意见内容');
    return;
  }

  if (auditActionType.value === 'reject' && !rejectReason.value.trim()) {
    message.error('请输入驳回理由');
    return;
  }

  if (auditLoading.value) {
    return; // 防止重复提交
  }

  auditLoading.value = true;
  try {
    const params: any = {
      id: currentAuditRecord.value.id,
    };

    if (auditActionType.value === 'approve') {
      // 同意操作
      if (auditType.value === 'audit') {
        // 技术审核同意
        params.auditContent = auditContent.value;
        params.auditStatus = 1;
      } else if (auditType.value === 'upAudit') {
        // 二次审批同意
        params.assignContent = auditContent.value;
        params.auditStatus = 2;
      }
    } else if (auditActionType.value === 'reject') {
      // 驳回操作
      if (auditType.value === 'audit') {
        // 技术审核驳回
        params.rejectReason = rejectReason.value;
        params.auditStatus = 99;
      } else if (auditType.value === 'upAudit') {
        // 二次审批驳回
        params.auditContent = rejectReason.value;
        params.auditStatus = 99;
      }
    }

    await defHttp.post({
      url: '/lims/employee/outTrainingAuditOrRollBack',
      params,
    });

    message.success(auditActionType.value === 'approve' ? '审核通过' : '驳回成功');
    auditModalVisible.value = false;
    reload();
  } catch (error) {
    // 可以在这里添加错误处理
  } finally {
    auditLoading.value = false;
  }
}

/**
 * 审核取消
 */
function handleAuditCancel() {
  auditModalVisible.value = false;
  auditActionType.value = '';
  auditContent.value = '';
  rejectReason.value = '';
  currentAuditRecord.value = {};
}

/**
 * 上传证书
 */
function handleUpload(record) {
  currentUploadRecord.value = record;
  certificateUrl.value = record.certificateUrl;
  uploadModalVisible.value = true;
}

/**
 * 上传证书确认
 */
async function handleUploadConfirm() {
  if (!certificateUrl.value) {
    message.error('请上传证书文件');
    return;
  }

  if (uploadLoading.value) {
    return; // 防止重复提交
  }

  uploadLoading.value = true;
  try {
    await defHttp.put({
      url: '/lims/employee/uploadOutTrainingData',
      data: {
        id: currentUploadRecord.value.id,
        certificateUrl: certificateUrl.value,
      },
    });

    message.success('证书上传成功');
    uploadModalVisible.value = false;
    reload();
  } catch (error) {
    // message.error('证书上传失败');
  } finally {
    uploadLoading.value = false;
  }
}

/**
 * 上传证书取消
 */
function handleUploadCancel() {
  uploadModalVisible.value = false;
  certificateUrl.value = '';
  currentUploadRecord.value = null;
}



/**
 * 成功回调
 */
function handleSuccess() {
  reload();
}
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '编辑',
      ifShow: () => {
        return record.auditStatus == '' || record.auditStatus == '1' || record.auditStatus == null || record.auditStatus == '99';
      },
      onClick: handleEdit.bind(null, record),
    },
  ];
}
/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  return [
    {
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
      },
      ifShow: () => {
        return record.status == '0';
      },
    },
    {
      label: '技术审核',
      auth: '一次审批',
      ifShow: () => {
        return record.auditStatus == '0';
      },
      onClick: handleAudit.bind(null, record),
    },
    {
      label: '二次审批',
      auth: '二次审批',
      ifShow: () => {
        return record.auditStatus == '1';
      },
      onClick: handleUpAudit.bind(null, record),
    },

    {
      label: '外出培训审批表',
      onClick: handleSeeSPB.bind(null, record),
    },
    {
      label: '上传证书',
      ifShow: () => {
        return record.auditStatus == '2';
      },
      onClick: handleUpload.bind(null, record),
    },
  ];
}
</script>
<style scoped></style>
