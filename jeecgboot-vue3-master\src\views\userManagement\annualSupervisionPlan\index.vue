<template>
  <div>
    <a-tabs @change="handleChange">
      <a-tab-pane key="1" tab="监察人员">
        <!--引用表格-->
        <BasicTable @register="registerTable" :rowSelection="rowSelection">
          <!--插槽:table标题-->
          <template #tableTitle>
            <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
            <!-- <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button> -->
            <!-- <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleAudit">
                    审核
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button>批量操作
                <Icon icon="mdi:chevron-down"></Icon>
              </a-button>
            </a-dropdown>
          </template>
          <!--操作栏-->
          <!-- :dropDownActions="getDropDownAction(record)" -->
          <template #action="{ record }">
            <TableAction :actions="getTableAction(record)" />
          </template>
        </BasicTable>
      </a-tab-pane>
      <a-tab-pane key="2" tab="监察计划" force-render>
        <BasicTable @register="registerTable2" :rowSelection="rowSelection2">
          <!--插槽:table标题-->
          <template #tableTitle>
            <!-- <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button> -->
            <!-- <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button> -->
            <!-- <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleAudit">
                    审核
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button>批量操作
                <Icon icon="mdi:chevron-down"></Icon>
              </a-button>
            </a-dropdown>
          </template>
          <!--操作栏-->
          <!-- :actions="getTableAction(record)" -->
          <template #action="{ record }">
            <TableAction :dropDownActions="getDropDownAction(record)" />
          </template>
        </BasicTable>

      </a-tab-pane>
    </a-tabs>


    <!-- 表单区域 -->
    <annualSupervisionPlanModal ref="annualSupervisionPlanModalRef" @register="registerModal" @success="handleSuccess" @auditSuccess="handleAuditSuccess"></annualSupervisionPlanModal>
    <!-- 审批表打印 -->
    <annualSupervisionPlanTableModal @register="registerSPBModal" @success="handleSuccess">
    </annualSupervisionPlanTableModal>


  </div>
</template>

<script lang="ts" name="userManagement-annualSupervisionPlan-index" setup>
import { ref } from 'vue';
import { BasicTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { useListPage } from '/@/hooks/system/useListPage';
import annualSupervisionPlanModal from './modules/annualSupervisionPlanModal.vue';
import annualSupervisionPlanTableModal from './modules/annualSupervisionPlanTableModal.vue';
import { columns, searchFormSchema, columns2, searchFormSchema2 } from './annualSupervisionPlan.data';
import { list, list2, getImportUrl, getExportUrl, returnOne } from './annualSupervisionPlan.api';
import { defHttp } from '/@/utils/http/axios';

//注册model
const [registerModal, { openModal }] = useModal();
const [registerSPBModal, { openModal: openSPBModal }] = useModal();

// 获取annualSupervisionPlanModal的引用
const annualSupervisionPlanModalRef = ref();


//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    title: '监察人员',
    api: list,
    columns,
    canResize: false,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      fieldMapToTime: [['timeRange', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    },
    showActionColumn: true,
    showTableSetting: false,
    actionColumn: {
      width: 120,
    },
  },
});
const { tableContext: tableContext2 } = useListPage({
  tableProps: {
    title: '年度监察计划',
    api: list2,
    columns: columns2,
    canResize: false,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema2,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      fieldMapToTime: [['timeRange', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    },
    showActionColumn: true,
    showTableSetting: false,
    actionColumn: {
      width: 120,
    },
  },
  exportConfig: {
    name: '年度监察计划',
    url: getExportUrl,
  },
  importConfig: {
    url: getImportUrl,
  },
});

const [registerTable, { reload, getSelectRows }, { rowSelection, selectedRowKeys }] = tableContext;
const [registerTable2, { reload: reload2, getSelectRows: getSelectRows2 }, { rowSelection: rowSelection2 }] = tableContext2;
function handleChange(e) {
  if (e == 1) {
    reload();
  } else {
    reload2()
  }
}
/**
 * 新增事件
 */
function handleAdd() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}
/**
 * 审核
 */
function batchHandleAudit() {
  defHttp.post({
    url: '/lims/employee/monitorPlanAuditAdd',
    params: getSelectRows(),
  })
}
/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  openModal(true, {
    record: [{ ...record }],
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 删除事件
 */
async function handleDelete(record) {
  await defHttp.get({ url: '/lims/employee/monitorPlanAuditDelete?id=' + record.id });
  reload2()
}
/**
 * 退回事件
 */
async function handleReturn(record) {
  await returnOne({ id: record.id }, reload2);
}
/**
 * 人员外出培训审批表
 */
function handleSeeSPB(record) {
  openSPBModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}

/**
 * 审核（使用annualSupervisionPlanModal中的审核弹窗）
 */
function handleAudit(record) {
  if (annualSupervisionPlanModalRef.value) {
    annualSupervisionPlanModalRef.value.openAuditModal(record);
  }
}

/**
 * 提交确认（使用annualSupervisionPlanModal中的提交弹窗）
 */
function handleConfirm(record) {
  if (annualSupervisionPlanModalRef.value) {
    annualSupervisionPlanModalRef.value.openSubmitModal(record);
  }
}

/**
 * 审核成功回调
 */
function handleAuditSuccess() {
  reload2();
}

/**
 * 成功回调
 */
function handleSuccess() {
  reload();
  reload2();
}
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '编辑',
      // ifShow: () => {
      //   return record.auditStatus == '0';
      // },
      onClick: handleEdit.bind(null, record),
    },
  ];
}
/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  return [
    {
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
      },
      ifShow: () => {
        return record.status == '0';
      },
    },
    {
      label: '提交',
      onClick: handleConfirm.bind(null, record),
      ifShow: () => {
        return record.auditStatus == null || record.auditStatus == '' || record.auditStatus == 99;
      },
    },
    {
      label: '审核',
      ifShow: () => {
        return record.auditStatus == '0';
      },
      onClick: handleAudit.bind(null, record),
    },
    {
      label: '年度监督计划表',
      onClick: handleSeeSPB.bind(null, record),
    },

  ];
}
</script>
<style scoped></style>
