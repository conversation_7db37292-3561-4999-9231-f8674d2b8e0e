<template>
<BasicModal v-bind="$attrs" :width="1000"
    @register="regSelReagentModal"
    :afterClose="onAfterClose"
    title="历史记录"
    :showOkBtn="okBtn"
    :showCancelBtn="cancelBtn"
    @ok="handleOk">
    
    <BasicTable @register="regiTable" rowKey="id">
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              label: '详情',
              icon: 'ant-design:eye-outlined',
              onClick: handleFileCheck.bind(null, record),
              ifShow: () => {
                return record.fileUrl != null && record.fileUrl.length > 0 && record.fileUrl.indexOf('.pdf') != -1;
              },
            },
            {
              label: '文件变更审批单',
              icon: 'ant-design:eye-outlined',
              onClick: handleTable.bind(null, record),
            },
          ]"
        />
      </template>
    </BasicTable>
    <FileViewPrintModal @register="regFileModal" @success="handleSuccess"></FileViewPrintModal>
    <planFileTableModal @register="regPlanFileTableModal"></planFileTableModal>
</BasicModal>
</template>

<script lang="ts" name="ReagentSelectRadio" setup>
import { reactive,ref } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
import { BasicTable, BasicColumn, FormSchema, useTable, TableAction } from '/@/components/Table';
import { useMessage } from '/@/hooks/web/useMessage';
import { json } from 'stream/consumers';
import { message } from 'ant-design-vue';
import { list } from '../PlanFile.api';
import FileViewPrintModal from './FileViewPrintModal.vue';
import planFileTableModal from './PlanFileTableModal.vue';


const { createMessage } = useMessage();
const emit = defineEmits(['ok']);
const [regFileModal, { openModal: openFileModal }] = useModal();
const [regPlanFileTableModal, { openModal:openPlanFileTableModal }] = useModal();
enum Api {
  list="/lims/employee/getFileInfoList",
}

let selected=ref([]);


const okBtn = ref(false);
const cancelBtn = ref(false);
const orderMainModel = reactive({
  id: null,
});
const [regSelReagentModal, {setModalProps, closeModal}] = useModalInner((data:any)=>{

  // getForm().resetFields();
  const pData=JSON.parse(JSON.stringify(data))
  Object.assign(orderMainModel, pData.record);
  console.log("🚀 ~ file: PlanDevelopmentDetailMod=useModalInner ~ pData:", orderMainModel)
  reload()
})

const queryParam: FormSchema[]=[
    {
      label: '文件编号',
      field: 'fileCode',
      component: 'Input',
      colProps: { span: 6 },
    },
    {
      label: '文件名称',
      field: 'fileName',
      component: 'Input',
      colProps: { span: 6 },
    },
];

const columns:BasicColumn[]=[
  {
    title: '文件编号',
    dataIndex: 'fileCode'
  },
  {
    title: '文件名称',
    dataIndex: 'fileName'
  },
  {
    title: 'SOP编号',
    dataIndex: 'sopCode'
  },
  {
    title: 'SOP名称',
    dataIndex: 'sopName'
  },
  {
    title: '版本',
    dataIndex: 'version'
  },
  {
    title: '打印次数',
    dataIndex: 'printCount'
  },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) => {
      if (text == '0') {
        return '正常';
      } else if (text == '1') {
        return '失效';
      } else {
        return text;
      }
    },
  },
  {
    title: '创建人',
    dataIndex: 'createUser_dictText'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime'
  },
  {
    title: '更新人',
    dataIndex: 'updateUser_dictText'
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime'
  },
  {
    title: '更新原因',
    dataIndex: 'updateReason'
  }
]

const [regiTable, { 
    reload,
    getSelectRows,
    getSelectRowKeys,
    setSelectedRowKeys,
    clearSelectedRowKeys,
    getForm,
}] = useTable({
  title: '详情',
  api: list,
  beforeFetch: (params)=>{
    params.status = '1';
    params.fileCode = orderMainModel.fileCode
    // clearSelectedRowKeys();
  },
  columns,
  rowKey: 'id',
  pagination: true,
  canResize: false,
  immediate: false,
  useSearchForm: false,
  showIndexColumn: false,
  formConfig: {
    labelCol: { span:8 },
    wrapperCol: { span:24 },
    schemas: queryParam,
    fieldMapToTime: [['pushRange', ['pushStartTime', 'pushEndTime'], 'YYYY-MM-DD']],
  },
  // 是否显示操作列
  showActionColumn: true,
  actionColumn: {
    fixed: 'left',
    width: 200,
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
  },
  showTableSetting: false,
  // rowSelection: {
  //   type: 'checkbox',
  // },
});

function handleOk(){
    // const selected=JSON.parse(JSON.stringify(getSelectRows()))
    // if(selected.length>0){
    //     emit('ok','reagentList', selected)
    //     closeModal()
    // }else{
    //     createMessage.warning('请至少选择一项')
    // }
}
function handleFileCheck(record) {
  openFileModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
function handleSuccess() {
  reload()
}
function onAfterClose(){

  clearSelectedRowKeys()
}
function handleTable(record) {
  openPlanFileTableModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
</script>

<style>
</style>