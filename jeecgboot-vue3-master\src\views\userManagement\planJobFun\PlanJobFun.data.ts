import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '姓名',
    dataIndex: 'userName'
  },
  {
    title: '岗位',
    dataIndex: 'position'
  },
  {
    title: '检测项目',
    dataIndex: 'sopName'
  },
  {
    title: '操作仪器',
    dataIndex: 'instrumentName',
    customRender: ({ record }) => {
      return record.instrumentName||'N/A';
    }
  },
  {
    title: '报告审核',
    dataIndex: 'reportAuditContent'
  },
  {
    title: '授权签发报告领域',
    dataIndex: 'reportAuthorizedArea'
  },
  {
    title: '其他授权',
    dataIndex: 'otherAuthorization'
  },
  {
    title: '备注',
    dataIndex: 'remark'
  },
];

export const searchFormSchema: FormSchema[] = [
  // {
  //   label: '名称',
  //   field: 'content',
  //   component: 'Input'
  // },
  // {
  //   label: '类型',
  //   field: 'type',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: '全部', value: '', key: '1' },
  //       { label: '正常', value: '0', key: '2' },
  //       { label: '失效', value: '1', key: '3' },
  //     ],
  //   },
  // },
  // {
  //   label: '状态',
  //   field: 'status',
  //   component: 'Select',
  //   defaultValue: '0',
  //   componentProps: {
  //     options: [
  //       { label: '正常', value: '0', key: '1' },
  //       { label: '失效', value: '1', key: '2' },
  //     ],
  //   },
  // },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  { label: '', field: 'id', component: 'Input', show: false },
  {
    label: '车间',
    field: 'workshopName',
    rules: [{ required: true }, { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }],
    component: 'Input',
  },
  {
    label: '洁净级别',
    field: 'cleanLevel',
    rules: [{ required: true }, { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }],
    component: 'Input',
  },
  {
    label: '检测区域',
    field: 'areaName',
    rules: [{ required: true }, { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }],
    component: 'Input',
  },
];
