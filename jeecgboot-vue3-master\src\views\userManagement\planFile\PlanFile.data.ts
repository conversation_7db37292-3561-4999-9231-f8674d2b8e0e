import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '文件编号',
    dataIndex: 'fileCode'
  },
  {
    title: '文件名称',
    dataIndex: 'fileName'
  },
  {
    title: 'SOP编号',
    dataIndex: 'sopCode'
  },
  {
    title: 'SOP名称',
    dataIndex: 'sopName'
  },
  {
    title: '版本',
    dataIndex: 'version'
  },
  {
    title: '打印次数',
    dataIndex: 'printCount'
  },

  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) => {
      if (text == '0') {
        return '正常';
      } else if (text == '1') {
        return '失效';
      } else {
        return text;
      }
    },
  },
  {
    title: '创建人',
    dataIndex: 'createUser_dictText'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime'
  },
  {
    title: '审核状态',
    dataIndex: 'auditStatus',
    customRender: ({ text }) => {
      if (text == '0') {
        return '提交完成';
      } else if (text == '1') {
        return '技术负责人审批完成';
      } else if (text == '2') {
        return '实验室管理员审批完成';
      } else if (text == '3') {
        return '综合管理员审核完成';
      } else if (text == '99') {
        return '驳回';
      } else {
        return text;
      }
    },
  },
  {
    title: '更新人',
    dataIndex: 'updateUser_dictText'
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime'
  },
  {
    title: '更新原因',
    dataIndex: 'updateReason'
  },
  {
    title: '授权人',
    dataIndex: 'authorizedPeopleName'
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '文件编号',
    field: 'fileCode',
    component: 'Input'
  },
  {
    label: '文件名称',
    field: 'fileName',
    component: 'Input'
  },
  {
    label: 'SOP编号',
    field: 'sopCode',
    component: 'Input'
  },
  {
    label: 'SOP名称',
    field: 'sopName',
    component: 'Input'
  },
  {
    label: '状态',
    field: 'auditStatus',
    component: 'Select',
    componentProps: {
      options: [
        { label: '全部', value: '', key: '1' },
        { label: '提交完成', value: '0', key: '2' },
        { label: '技术负责人审批完成', value: '1', key: '3' },
        { label: '实验室管理员审批完成', value: '2', key: '4' },
        { label: '综合管理员审核完成', value: '3', key: '5' },
        { label: '驳回', value: '99', key: '6' }
      ],
    },
  },
  {
    label: '是否作废',
    field: 'status',
    component: 'Select',
    defaultValue: '0',
    componentProps: {
      options: [
        { label: '否', value: '0', key: '1' },
        { label: '是', value: '2', key: '4' },
      ],
    },
  },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  { label: '', field: 'id', component: 'Input', show: false },
  {
    label: '车间',
    field: 'workshopName',
    rules: [{ required: true }, { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }],
    component: 'Input',
  },
  {
    label: '洁净级别',
    field: 'cleanLevel',
    rules: [{ required: true }, { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }],
    component: 'Input',
  },
  {
    label: '检测区域',
    field: 'areaName',
    rules: [{ required: true }, { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }],
    component: 'Input',
  },
];
